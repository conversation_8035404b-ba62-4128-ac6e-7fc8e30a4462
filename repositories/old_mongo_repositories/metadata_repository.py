from typing import List, Dict

from pymongo import MongoClient

from configuration import config
from schemas.responses import ProductMetadata
from repositories.metadata_repository_interface import MetadataRepositoryInterface


class MetadataRepository(MetadataRepositoryInterface):
    """
    A repository class to interact with the MongoDB database for fetching product metadata.

    Methods:
        get_metadata_for_ISINs(ISINs: List[str] = None) -> List[ProductMetadata]:
            Fetches metadata for given ISINs. If no ISINs are provided, fetches metadata for all products.

        get_fields_for_ISINs(ISINs: List[str], fields: List[str]) -> Dict[str, List]:
            Fetches specified fields for a given list of ISINs.

        get_asset_class_ISINs() -> Dict[str, List[str]]:
            Fetches asset classes and their corresponding ISINs.

        get_ISINs() -> List[str]:
            Fetches all ISINs in the database.
    """

    def __init__(self):
        self.settings = config.get_settings()

# TODO remove dev flag
    def get_metadata_for_ISINs(self, ISINs: list[str] = None, dev=False) -> List[ProductMetadata]:
        with MongoClient(self.settings.db_url) as client:
            if dev:
                db = client['dev']
            else:
                db = client[self.settings.db_name]
            metadata_collection = db[self.settings.metadata_collection]

            products = []

            if ISINs is None:
                result = metadata_collection.find({}, {'_id': False})
                for item in result:
                    product = ProductMetadata.model_validate(item)
                    products.append(product)

            else:
                for ISIN in ISINs:
                    result = metadata_collection.find_one({"isin": ISIN}, {'_id': False})
                    product = ProductMetadata.model_validate(result)
                    products.append(product)

        return products

    def get_fields_for_ISINs(self, ISINs: List[str], fields: List[str]) -> Dict[str, List]:
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            metadata_collection = db[self.settings.metadata_collection]

            projection = {field: 1 for field in fields}
            projection['_id'] = 0

            pipeline = [
                {"$match": {"isin": {"$in": ISINs}}},
                {"$addFields": {"sortOrder": {"$indexOfArray": [ISINs, "$isin"]}}},
                {"$sort": {"sortOrder": 1}},
                {"$project": projection}
            ]

            result = list(metadata_collection.aggregate(pipeline))

            result_dict = {}

            for field in fields:
                result_dict[field] = [product[field] for product in result]

            return result_dict

    def get_asset_class_ISINs(self) -> Dict[str, str]:
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            metadata_collection = db[self.settings.metadata_collection]

            pipeline = [
                {
                    '$group': {
                        '_id': '$assetClass',  # Group by 'assetClass'
                        'isins': {'$addToSet': '$isin'}  # Collect unique 'isin' values into 'isins' array
                    }
                }
            ]

            result = list(metadata_collection.aggregate(pipeline))

            # Format the result into a dictionary where assetClass is the key
            asset_classes_isins = {item['_id']: item['isins'] for item in result if item['_id'] != 'Risk Free'}

        return asset_classes_isins

    def get_ISINs(self) -> List[str]:
        with MongoClient(self.settings.db_url) as client:
            db = client[self.settings.db_name]
            metadata_collection = db[self.settings.metadata_collection]

            result = list(metadata_collection.find({}, {'_id': False, 'isin': True}).sort('_id', 1))

            isins = [product['isin'] for product in result]

            return isins
