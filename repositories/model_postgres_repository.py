import psycopg2
import pandas as pd
from datetime import datetime
from typing import List, Optional
from configuration import config, config_params


class ModelPostgresRepository:
    def __init__(self):
        self.connection_params = config_params.CONNECTION_PARAMS
        self.expected_returns_table = config_params.ASSET_LISTING_EXPECTED_RETURNS_TABLE
        self.covariance_table = config_params.ASSET_LISTING_COVARIANCES_TABLE

    def get_connection(self):
        """Create a new database connection with enhanced settings for reliability."""
        import time

        # Force close any existing connections to avoid pool issues
        try:
            # Create connection with enhanced parameters
            connection_params = self.connection_params.copy()
            connection_params.update({
                'connect_timeout': 30,
                'application_name': 'model_api_save_operations',
                # Force a new connection by adding a unique parameter
                'options': f'-c statement_timeout=300000'  # 5 minutes
            })

            conn = psycopg2.connect(**connection_params)
            conn.autocommit = False  # Ensure we control transactions

            # Test the connection
            with conn.cursor() as cur:
                cur.execute("SELECT 1")

            print(f"Successfully created fresh database connection")
            return conn

        except psycopg2.Error as e:
            print(f"Failed to create database connection: {e}")
            raise

    def save_expected_returns(self, expected_returns: pd.DataFrame, date: Optional[datetime] = None):
        date = date or datetime.now()
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with self.get_connection() as conn:
                    with conn.cursor() as cur:
                        for _, row in expected_returns.iterrows():
                            cur.execute(
                                f"""
                                INSERT INTO {self.expected_returns_table}
                                    (asset_listing_id, calculation_date, expected_return)
                                VALUES (
                                    (SELECT id FROM asset_listings WHERE asset_listing_code = %s),
                                    %s,
                                    %s
                                )
                                ON CONFLICT (asset_listing_id, calculation_date) DO UPDATE
                                SET expected_return = EXCLUDED.expected_return
                                """,
                                (row['asset_listing_code'], date, row['expected_return'])
                            )
                        conn.commit()
                        print(f"Successfully saved expected returns on attempt {attempt + 1}")
                        return  # Success, exit retry loop

            except psycopg2.InterfaceError as e:
                print(f"Connection interface error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(2 ** attempt)  # Exponential backoff
                    print(f"Retrying save_expected_returns (attempt {attempt + 2}/{max_retries})...")
                else:
                    print(f"Failed to save expected returns after {max_retries} attempts")
                    raise
            except psycopg2.Error as e:
                print(f"Database error in save_expected_returns on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(2 ** attempt)
                    print(f"Retrying save_expected_returns (attempt {attempt + 2}/{max_retries})...")
                else:
                    raise
            except Exception as e:
                print(f"Unexpected error in save_expected_returns: {e}")
                raise

    def get_expected_returns_for_asset_listings(
            self,
            asset_listing_codes: Optional[List[str]] = None,
            date: Optional[datetime] = None
    ) -> pd.DataFrame:
        date = date or datetime.now()
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                sql = f"""
                    SELECT DISTINCT ON (al.asset_listing_code)
                        al.asset_listing_code,
                        er.expected_return
                    FROM {self.expected_returns_table} er
                    JOIN asset_listings al ON er.asset_listing_id = al.id
                    WHERE er.calculation_date <= %s
                """
                params = [date]
                if asset_listing_codes:
                    sql += " AND al.asset_listing_code = ANY(%s)"
                    params.append(asset_listing_codes)
                sql += " ORDER BY al.asset_listing_code, er.calculation_date DESC"
                cur.execute(sql, params)
                rows = cur.fetchall()
                columns = ['asset_listing_code', 'expected_return']
        return pd.DataFrame(rows, columns=columns) if rows else pd.DataFrame(columns=columns)

    def save_covariance(self, covariance_df: pd.DataFrame, date: Optional[datetime] = None):
        date = date or datetime.now()

        codes = covariance_df.columns.tolist()
        upper_triangle_records = []

        for i, code1 in enumerate(codes):
            for j, code2 in enumerate(codes):
                if j >= i:
                    covariance = float(covariance_df.iloc[i, j])
                    upper_triangle_records.append((code1, code2, covariance, date))

        if not upper_triangle_records:
            return

        # Force garbage collection to clean up any lingering connections
        import gc
        gc.collect()

        # Add a small delay to ensure any previous connections are fully closed
        import time
        time.sleep(0.5)

        conn = None
        try:
            print("Creating fresh connection for covariance save...")
            conn = self.get_connection()

            with conn.cursor() as cur:
                print(f"Fetching asset listing IDs for {len(codes)} codes...")
                cur.execute(
                    "SELECT asset_listing_code, id FROM asset_listings WHERE asset_listing_code = ANY(%s)",
                    (codes,)
                )
                code_to_id = dict(cur.fetchall())
                print(f"Found {len(code_to_id)} matching asset listings")

                db_records = []
                for code1, code2, covariance, date in upper_triangle_records:
                    id1 = code_to_id.get(code1)
                    id2 = code_to_id.get(code2)
                    if id1 is not None and id2 is not None:
                        db_records.append((id1, id2, covariance, date))

                if not db_records:
                    print("No valid records to save")
                    return

                print(f"Preparing to save {len(db_records)} covariance records in batches...")

                # Process in batches to avoid SSL timeout
                batch_size = 10000  # Process 10k records at a time
                total_batches = (len(db_records) + batch_size - 1) // batch_size

                for batch_num in range(total_batches):
                    start_idx = batch_num * batch_size
                    end_idx = min((batch_num + 1) * batch_size, len(db_records))
                    batch_records = db_records[start_idx:end_idx]

                    print(f"Processing batch {batch_num + 1}/{total_batches} ({len(batch_records)} records)...")

                    values_sql = b','.join(
                        cur.mogrify("(%s,%s,%s,%s)", r) for r in batch_records
                    ).decode('utf-8')

                    cur.execute(f"""
                        INSERT INTO {self.covariance_table}
                        (asset_listing_id1, asset_listing_id2, covariance, calculation_date)
                        VALUES {values_sql}
                        ON CONFLICT (asset_listing_id1, asset_listing_id2, calculation_date)
                        DO UPDATE SET covariance = EXCLUDED.covariance
                    """)

                    # Commit each batch to avoid long-running transactions
                    conn.commit()
                    print(f"✓ Batch {batch_num + 1} committed successfully")

                print("✓ All covariance data saved successfully!")

        except Exception as e:
            print(f"Error in save_covariance: {e}")
            if conn:
                try:
                    conn.rollback()
                    print("Transaction rolled back")
                except:
                    pass
            raise
        finally:
            if conn:
                try:
                    conn.close()
                    print("Connection closed")
                except:
                    pass

    def get_covariance_for_asset_listings(
            self,
            asset_listing_codes: Optional[List[str]] = None,
            date: Optional[datetime] = None
    ) -> pd.DataFrame:
        date = date or datetime.now()
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                query = f"""
                    SELECT DISTINCT ON (al1.asset_listing_code, al2.asset_listing_code)
                        al1.asset_listing_code AS asset_listing_code1,
                        al2.asset_listing_code AS asset_listing_code2,
                        c.covariance
                    FROM {self.covariance_table} c
                    JOIN asset_listings al1 ON c.asset_listing_id1 = al1.id
                    JOIN asset_listings al2 ON c.asset_listing_id2 = al2.id
                    WHERE c.calculation_date <= %s
                """
                params = [date]
                if asset_listing_codes:
                    query += " AND al1.asset_listing_code = ANY(%s) AND al2.asset_listing_code = ANY(%s)"
                    params.extend([asset_listing_codes, asset_listing_codes])
                query += " ORDER BY al1.asset_listing_code, al2.asset_listing_code, c.calculation_date DESC"
                cur.execute(query, params)
                rows = cur.fetchall()
                columns = ['asset_listing_code1', 'asset_listing_code2', 'covariance']
        if not rows:
            return pd.DataFrame(0.0, index=asset_listing_codes, columns=asset_listing_codes)
        df = pd.DataFrame(rows, columns=columns)
        matrix = df.pivot(index='asset_listing_code1', columns='asset_listing_code2', values='covariance')
        if asset_listing_codes:
            matrix = matrix.reindex(index=asset_listing_codes, columns=asset_listing_codes)
        full_matrix = matrix.combine_first(matrix.T)
        return full_matrix
