from datetime import datetime

import pandas as pd
import numpy as np
import os
from abc import ABC, abstractmethod
from typing import List

from schemas.requests import EfficientFrontierRequest, MultiPeriodEfficientFrontierRequest
from schemas.constants import TargetMeasureEnum
from schemas.pydantic_models import PortfolioPurchaseLots, Product, FrontierPortfolio, FrontierStrategy, ProductMetadata
from models import benchmark_model_postgres as benchmark_model
from optimizers import efficient_frontier_optimizers
from repositories.old_mongo_repositories import etf_repository, metadata_repository, risk_free_rate_repository
from configuration import config, logging_config
from services import visualization_service, model_service
from utils import util
from utils.typing_util import EfficientFrontierR<PERSON>ult
from optimizers import discretizers
from schemas.responses import MultiPeriodEfficientFrontierResponse

logger = logging_config.get_logger(__name__)


class PortfolioEfficientFrontierServiceInterface(ABC):
    """
    Abstract base class for calculating an efficient frontier.

    Attributes:
        model (object): Model for equilibrium pricing.
        optimizer (object): PortfolioOptimizer for efficient frontier.
        capm_optimizer (object): CAPM-based optimizer.
        settings (object): Configuration settings.
        tracking_error_discretizer (object): Discretizer for tracking error.
        etf_repository (object): Repository for ETF data.
        risk_free_rate_repository (object): Repository for risk-free rate data.
    """

    def __init__(self):
        self.model = benchmark_model.RegressionPricingModel()
        self.optimizer = efficient_frontier_optimizers.MeanVarianceOptimizer()
        self.capm_optimizer = efficient_frontier_optimizers.CapmOptimizer()
        self.settings = config.get_settings()
        self.tracking_error_discretizer = discretizers.TrackingErrorDiscretizer()
        self.etf_repository = etf_repository.EtfRepository()
        self.risk_free_rate_repository = risk_free_rate_repository.RiskFreeRateRepository()
        self.model_service = model_service.ModelService()

    @abstractmethod
    def calculate_efficient_frontier(self, request: EfficientFrontierRequest, save_analytics: bool = False, minimize_tracking_error: bool = False) -> EfficientFrontierResult:
        """
        Abstract method for calculating the efficient frontier.

        Args:
            request (EfficientFrontierRequest): Input data for the calculation.
            save_analytics (bool): Whether to save analytical outputs.
            minimize_tracking_error (bool): Whether to minimize tracking error.

        Returns:
            EfficientFrontierResult: Namedtuple containing:
                - quantities_df (pd.DataFrame): Quantities DataFrame for discrete efficient frontier.
                - weights_df (pd.DataFrame): Weights DataFrame for discrete efficient frontier.
                - product_returns (np.ndarray): Numpy array of product_returns.
                - product_covariance (np.ndarray): Numpy array of product_covariance matrix.
                - optimal_index (int): Index of the optimal portfolio.
        """
        pass

    def _get_expected_returns_and_covariance(self, isins: List[str] = None, calculate_on_datetime: datetime = None):
        if calculate_on_datetime is None:
            calculate_on_datetime = datetime.now()
        logger.debug('Expected product returns and covariance are not given in the request. Calling pricing and risk models')
        expected_returns_df, covariance_df = self.model_service.get_expected_returns_and_covariance(isins = isins, calculate_on_date=calculate_on_datetime)
        return expected_returns_df.values.reshape(-1, 1), covariance_df.values


class PortfolioEfficientFrontierService(PortfolioEfficientFrontierServiceInterface):

    def __init__(self):
        super(PortfolioEfficientFrontierService, self).__init__()

    def calculate_efficient_frontier(self, request: EfficientFrontierRequest, save_analytics: bool = False, use_risk_free: bool = True) -> EfficientFrontierResult:
        """
        Calculate the efficient frontier.

        Args:
            request (EfficientFrontierRequest): Input data for the calculation.
            save_analytics (bool): Whether to save analytics data.
            use_risk_free (bool): Whether to include risk-free rate in calculations.

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, np.ndarray, int]:
                - Quantities DataFrame for discrete efficient frontier.
                - Weights DataFrame for discrete efficient frontier.
                - Numpy array of product_returns.
                - Numpy array of product_covariance matrix.
                - Index of the optimal portfolio.
        """
        investment_amount = request.investment_amount
        last_date = request.calculate_on_datetime

        current_risk_free_rate = self.risk_free_rate_repository.get_last_risk_free_rate(last_date) if use_risk_free else 0.0

        returns, covariance = self._get_expected_returns_and_covariance()

        efficient_frontier, efficient_frontier_returns, efficient_frontier_variances = self.capm_optimizer.calculate_efficient_frontier(returns, covariance)

        prices_df, risk_free_price_df = self.etf_repository.get_prices_eur(last_date)
        prices_df = pd.concat([prices_df, risk_free_price_df], axis=1)

        prices = prices_df.values.reshape((-1, 1))

        quantities_discrete_efficient_frontier_df = self.tracking_error_discretizer.discretize_efficient_frontier(efficient_frontier,
                                                                                                                  prices_df,
                                                                                                                  investment_amount,
                                                                                                                  covariance)

        weights_discrete_efficient_frontier_df, initial_values = util.calculate_weights_for_discrete_frontier(quantities_discrete_efficient_frontier_df, prices)
        weights_discrete_efficient_frontier_df = util.calculate_return_variance_sharpe(weights_discrete_efficient_frontier_df,
                                                                                       returns,
                                                                                       covariance,
                                                                                       current_risk_free_rate)

        weights_discrete_efficient_frontier_df = weights_discrete_efficient_frontier_df.sort_values(by='variance')
        quantities_discrete_efficient_frontier_df = quantities_discrete_efficient_frontier_df.loc[weights_discrete_efficient_frontier_df.index]

        portfolio, weights, return_, variance, sharpe, optimal_index = util.find_max_sharpe_portfolio(
            weights_discrete_efficient_frontier_df,
            quantities_discrete_efficient_frontier_df
        )

        if save_analytics:
            prefix = util.get_last_year_and_month(last_date)

            directory_path = f'{self.settings.analytics_path}{prefix}/{investment_amount}/'
            os.makedirs(os.path.dirname(directory_path), exist_ok=True)

            efficient_frontier_df = pd.DataFrame(efficient_frontier, columns=prices_df.columns)
            efficient_frontier_df = util.calculate_return_variance_sharpe(efficient_frontier_df,
                                                                          returns,
                                                                          covariance,
                                                                          current_risk_free_rate)

            efficient_frontier_df.to_csv(self.settings.analytics_path + f'{prefix}/efficient_frontier.csv', index=False)

            quantities_discrete_efficient_frontier_df.to_csv(self.settings.analytics_path + f'{prefix}/{investment_amount}/quantities_discrete_efficient_frontier.csv',
                                                             index=False)
            weights_discrete_efficient_frontier_df.to_csv(self.settings.analytics_path + f'{prefix}/{investment_amount}/discrete_efficient_frontier.csv',
                                                          index=False)

        return quantities_discrete_efficient_frontier_df, weights_discrete_efficient_frontier_df, returns, covariance, optimal_index


class StrategyEfficientFrontierServiceInterface(ABC):
    def __init__(self):
        self.model = benchmark_model.RegressionPricingModel()
        self.strategy_optimizer = efficient_frontier_optimizers.MultiPeriodStrategyOptimizer()
        self.portfolio_optimizer = efficient_frontier_optimizers.CapmOptimizer()
        self.settings = config.get_settings()
        self.tracking_error_discretizer = discretizers.TrackingErrorDiscretizer()
        self.etf_repository = etf_repository.EtfRepository()
        self.risk_free_rate_repository = risk_free_rate_repository.RiskFreeRateRepository()
        self.metadata_repository = metadata_repository.MetadataRepository()
        self.visualization_service = visualization_service.VisualizationService()
        self.model_service = model_service.ModelService()

    def calculate_efficient_frontier(self, request: MultiPeriodEfficientFrontierRequest):
        pass

    def _get_expected_returns_and_covariance(self, isins: List[str] = None, calculate_on_datetime: datetime = None):
        if calculate_on_datetime is None:
            calculate_on_datetime = datetime.now()
        logger.debug('Expected product returns and covariance are not given in the request. Calling pricing and risk models')
        expected_returns_df, covariance_df = self.model_service.get_expected_returns_and_covariance(isins = isins, calculate_on_date=calculate_on_datetime)


        cols_new = [col for col in covariance_df.columns if col != 'FR0010510800'] + ['FR0010510800']
        expected_returns_df = expected_returns_df.reindex(columns=cols_new)
        covariance_df = covariance_df.reindex(columns=cols_new, index=cols_new)

        return expected_returns_df, covariance_df

    @staticmethod
    def _calculate_market_value(current_portfolio: PortfolioPurchaseLots, current_prices: np.ndarray):
        if current_portfolio is None or current_portfolio.is_empty():
            logger.debug('User\'s current portfolio is empty.')
            return 0
        logger.debug(f'User\'s portfolio contains {len(current_portfolio.asset_purchase_lots)} assets. Calculating market value...')
        mark_to_market_value = current_portfolio.get_quantities().T @ current_prices
        return mark_to_market_value

    def _find_optimal_strategy_index(self, efficient_frontier_strategy_returns: np.ndarray, efficient_frontier_strategy_risks: np.ndarray, target_value: float, target_measure: TargetMeasureEnum):
        if target_measure == TargetMeasureEnum.RISK:
            return self._find_optimal_strategy_index_for_target_risk(efficient_frontier_strategy_risks, -target_value)
        else:
            return self._find_optimal_strategy_index_for_target_return(efficient_frontier_strategy_returns, target_value)

    @staticmethod
    def _find_optimal_strategy_index_for_target_risk(efficient_frontier_strategy_risks: np.ndarray, target_risk: float):
        optimal_strategy_index = np.searchsorted(efficient_frontier_strategy_risks.squeeze(), target_risk, side='right') - 1

        if optimal_strategy_index < 0:
            return 0

        return optimal_strategy_index

    @staticmethod
    def _find_optimal_strategy_index_for_target_return(efficient_frontier_strategy_returns: np.ndarray, target_return: float):
        optimal_strategy_index = np.searchsorted(efficient_frontier_strategy_returns.squeeze(), target_return, side='left')

        if optimal_strategy_index >= len(efficient_frontier_strategy_returns):
            return len(efficient_frontier_strategy_returns) - 1

        return optimal_strategy_index


class StrategyEfficientFrontierService(StrategyEfficientFrontierServiceInterface):
    def __init__(self):
        super(StrategyEfficientFrontierService, self).__init__()

    def calculate_efficient_frontier(self, request: MultiPeriodEfficientFrontierRequest):
        logger.info(f"Starting efficient frontier calculation for investment horizon: {request.investment_horizon} periods")
        #TODO add isins when we add them in request
        returns_df, covariance_df = self._get_expected_returns_and_covariance(isins=None, calculate_on_datetime=request.calculate_on_datetime)
        returns = returns_df.values.reshape(-1, 1)
        covariance = covariance_df.values
        logger.info(f"Retrieved data for {len(returns)} products")

        prices, prices_df = self._get_prices(request)

        efficient_frontier_portfolios, portfolio_returns, portfolio_risks = self.portfolio_optimizer.calculate_efficient_frontier(returns, covariance)
        efficient_frontier_df = pd.DataFrame(efficient_frontier_portfolios, columns=returns_df.columns)

        # TODO write calculate_market_value so it product_returns 0 if current_portfolio is not given, do it after checking what happens when current_portfolio is not given in request
        market_value = self._calculate_market_value(request.current_portfolio, prices)

        cash_flows = np.array(request.cash_flows)
        cash_flows[0] += market_value

        (efficient_frontier_strategies,
         efficient_frontier_strategy_returns,
         efficient_frontier_strategy_risks) = self.strategy_optimizer.calculate_efficient_frontier(
            product_expected_returns=returns,
            product_covariance=covariance,
            sp_efficient_frontier=efficient_frontier_df,
            investment_horizon=request.investment_horizon,
            cash_flows=cash_flows,
            percentile=self.settings.default_risk_percentile,
            user_preferences=request.user_preferences,
            preferred_measure=request.target_measure,
        )

        invested_amount = np.sum(request.cash_flows)
        relative_target_value = (request.target_value - invested_amount) / invested_amount

        logger.info(f"Finding optimal strategy for target {request.target_measure}: {request.target_value}, relative target value: {relative_target_value}")
        optimal_strategy_index = self._find_optimal_strategy_index(efficient_frontier_strategy_returns, efficient_frontier_strategy_risks, relative_target_value, request.target_measure)

        product_metadata = self._get_metadata(prices_df)
        frontier_portfolios = self._get_frontier_portfolios(efficient_frontier_df, portfolio_returns, portfolio_risks)
        frontier_strategies = self._get_frontier_strategies(efficient_frontier_strategies, efficient_frontier_strategy_returns, efficient_frontier_strategy_risks, request.investment_horizon)

        sp_ef_simulated_returns = self.strategy_optimizer.simulate_sp_ef_returns(efficient_frontier_df)
        frontier_projections = self.visualization_service.calculate_frontier_projections(efficient_frontier_strategies, sp_ef_simulated_returns, cash_flows, request.user_preferences)
        frontier_weights = self.visualization_service.calculate_frontier_weights(efficient_frontier_strategies, efficient_frontier_df, product_metadata)

        response = MultiPeriodEfficientFrontierResponse(
            product_metadata=product_metadata,
            portfolio_frontier=frontier_portfolios,
            strategy_frontier=frontier_strategies,
            optimal_strategy_index=optimal_strategy_index,
            frontier_projections=frontier_projections,
            frontier_weights=frontier_weights,
        )

        return response

    def _get_prices(self, request: MultiPeriodEfficientFrontierRequest) -> (np.ndarray, pd.DataFrame):
        logger.debug('Calculating prices.')
        prices_df, risk_free_price_df = self.etf_repository.get_prices_eur(request.calculate_on_datetime)
        prices_df = pd.concat([prices_df, risk_free_price_df], axis=1)

        prices = prices_df.values.reshape((-1, 1))
        return prices, prices_df

    def _get_metadata(self, prices_df: pd.DataFrame) -> List[ProductMetadata]:
        product_metadata = self.metadata_repository.get_metadata_for_ISINs(prices_df.columns)
        for product in product_metadata:
            product.price = prices_df[product.isin].item()
        return product_metadata

    def _get_frontier_portfolios(self, efficient_frontier_df: pd.DataFrame, portfolio_returns: np.ndarray, portfolio_risks: np.ndarray) -> List[FrontierPortfolio]:
        frontier_portfolios = []
        for index, weights in efficient_frontier_df.iterrows():
            filtered_weights = weights[weights != 0.0]

            products = []

            for isin in filtered_weights.index.values:
                products.append(Product(
                    isin=isin,
                    weight=filtered_weights[isin].item()
                ))

            frontier_portfolios.append(FrontierPortfolio(
                index=index,
                annual_return=portfolio_returns[index].item(),
                variance=portfolio_risks[index].item(),
                products=products
            ))

        return frontier_portfolios

    def _get_frontier_strategies(self,
                                 efficient_frontier_strategies: np.ndarray,
                                 efficient_frontier_strategy_returns: np.ndarray,
                                 efficient_frontier_strategy_risks: np.ndarray,
                                 investment_horizon: int
                                 ) -> List[FrontierStrategy]:
        # TODO move annualization to risk_measure/return_measure classes
        annual_strategy_returns = util.get_annual_from_multi_period_return(efficient_frontier_strategy_returns, investment_horizon)
        annual_strategy_risks = -util.get_annual_from_multi_period_return(-efficient_frontier_strategy_risks, investment_horizon)

        frontier_strategies = []
        for index, strategy in enumerate(efficient_frontier_strategies):
            frontier_strategies.append(FrontierStrategy(
                index=index,
                annual_return=annual_strategy_returns[index].item(),
                annual_risk=annual_strategy_risks[index].item(),
                strategy=strategy,
            ))
        return frontier_strategies
